const Mock = require('mockjs')

const datas = Mock.mock({

})
module.exports = [
  // {
  //   url: '/authModel/queryAuthModel',
  //   type: 'post',
  //   response: config => {
  //     const items = datas.items
  //     return {
  //       code: 20000,
  //       data: items
  //     }
  //   }
  // },
  // {
  //   url: '/chart/view/getData/',
  //   type: 'post',
  //   response: config => {
  //     const items = getDatas.data
  //     return {
  //       code: 20000,
  //       data: items
  //     }
  //   }
  // }

]
