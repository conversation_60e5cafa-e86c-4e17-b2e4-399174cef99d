import request from '@/utils/axios-api'

// 根据方案ID获取文档章节/目录
export function getChapters(data) {
  return request({
    url: '/api/wiki/catalog/listByBiz',
    method: 'post',
    data
  })
}
// 添加章节/文档目录
export function addChapter(data) {
  return request({
    url: '/api/wiki/catalog',
    method: 'post',
    data
  })
}
// 批量添加章节/文档目录
export function batchAddChapter(data) {
  return request({
    url: '/api/wiki/catalog/saveBatch',
    method: 'post',
    data
  })
}
// 根据章节id查询文档
export function getDocByCatalogId(data) {
  return request({
    url: '/api/wiki/catalog/getDocumentsByCatalogId',
    method: 'get',
    data
  })
}
// 删除章节
export function delChapter(data) {
  return request({
    url: '/api/wiki/catalog',
    method: 'delete',
    data
  })
}
// 新增文档内容
export function addDocument(data) {
  return request({
    url: '/api/wiki/wiki/document',
    method: 'post',
    data
  })
}
// 编辑文档内容
export function editDocument(data) {
  return request({
    url: '/api/wiki/wiki/document',
    method: 'put',
    data
  })
}
// 设置章节负责人
export function addChapterLeader(data) {
  return request({
    url: '/api/wiki/catalog/setCatalogLeadingBy',
    method: 'post',
    data
  })
}
// 查询章节信息
export function getChapterDetail(id) {
  return request({
    url: `/api/wiki/catalog/${id}`,
    method: 'get'
  })
}
// 获取当前章节可流转状态
export function getChapterStatus(id) {
  return request({
    url: `/api/wiki/catalog/findNextNode/${id}`,
    method: 'get'
  })
}
// 章节状态流转
export function flowChapterStatus(catalogId, sourceStateCode, targetStateCode) {
  return request({
    url: `/api/wiki/catalog/transitionState/${catalogId}/${sourceStateCode}/${targetStateCode}`,
    method: 'put'
  })
}
// 根据方案id获取所有文档内容
export function getAllDocument(data) {
  return request({
    url: '/api/wiki/catalog/allDocument',
    method: 'post',
    data
  })
}
