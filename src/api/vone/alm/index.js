import request from '@/utils/axios-api'

// 查询事项类型
export function apiAlmGetTypeNoPage(projectId, typeClassify) {
  return request({
    url: `/api/alm/alm/projectTypeConfig/findByProjectIdAndTypeClassify/${projectId}/${typeClassify}`,
    method: 'GET',
  })
}
/**
 * @description 查询缺陷类型
 * @param {*}
 */
export function getDefectTypeList() {
  return request({
    url: `/api/alm/alm/projectTypeConfig/findByTypeClassify/BUG`,
    method: 'get',
  })
}

// 查询事项类型
export function getAlmGetTypeNoPage(typeClassify) {
  return request({
    url: `/api/alm/alm/projectTypeConfig/findByTypeClassify/${typeClassify}`,
    method: 'GET',
  })
}

// 新增事项类型
export function apiAlmTypeAdd(data) {
  return request({
    url: '/api/alm/alm/type',
    method: data.id ? 'put' : 'post',
    data,
  })
}
// 复制事项类型
export function apiAlmTypeCopy(data) {
  return request({
    url: '/api/alm/alm/type/copy',
    method: 'post',
    data,
  })
}

// 事项类型-删除
export function apiAlmTypeDel(data) {
  return request({
    url: '/api/alm/alm/type',
    method: 'DELETE',
    data,
  })
}

// 新增优先级
export function apiAlmPriorityAdd(data) {
  return request({
    url: '/api/alm/alm/priority',
    method: data.id ? 'put' : 'post',
    data,
  })
}

// 查询优先级
export function apiAlmPriorityPage(data) {
  return request({
    url: '/api/alm/alm/priority/page',
    method: 'post',
    data,
  })
}

// 查询来源
export function apiAlmSourcePage(data) {
  return request({
    url: '/api/alm/alm/source/page',
    method: 'post',
    data,
  })
}

// 新增来源
export function apiAlmSourceAdd(data) {
  return request({
    url: '/api/alm/alm/source',
    method: data.id ? 'put' : 'post',
    data,
  })
}

// 优先级--不分页
export function apiAlmPriorityNoPage(data) {
  return request({
    url: '/api/alm/alm/priority/query',
    method: 'post',
    data,
  })
}

// 事项类型--不分页
export function apiAlmTypeNoPage(data) {
  return request({
    url: '/api/alm/alm/type/query',
    method: 'post',
    data,
  })
}

// 查询事项类型
export function getAlmGetTypeById(id) {
  return request({
    url: `/api/alm/alm/type/${id}`,
    method: 'GET',
  })
}

// 来源
export function getAlmGetSourceId(id) {
  return request({
    url: `/api/alm/alm/source/${id}`,
    method: 'GET',
  })
}

// 优先级
export function getAlmGetPriorityId(id) {
  return request({
    url: `/api/alm/alm/priority/${id}`,
    method: 'GET',
  })
}

// 需求关联测试用例-查询
export function getCaseByRequirementId(id) {
  return request({
    url: `/api/alm/issueRequirementTestCase/getCaseByRequirementId/${id}`,
    method: 'GET',
  })
}

// 需求关联测试用例-关联
export function issueRequirementTestCase(data) {
  return request({
    url: '/api/alm/issueRequirementTestCase/associate',
    method: 'post',
    data,
  })
}

// 需求关联测试用例-取消关联
export function issueTestCaseDel(data) {
  return request({
    url: '/api/alm/issueRequirementTestCase/disassociate',
    method: 'DELETE',
    data,
  })
}

// 查询标签列表
export function apiAlmTagPage(data) {
  return request({
    url: '/api/alm/issueTag/pageTab',
    method: 'post',
    data,
  })
}

// 标签拖拽
export function apiAlmTabDrag(data) {
  return request({
    url: '/api/alm/issueTag/tabDrag',
    method: 'put',
    data,
  })
}

// 标签删除
export function issueTagDel(data) {
  return request({
    url: '/api/alm/issueTag',
    method: 'DELETE',
    data,
  })
}

// 标签查询
export function findTagsByNameWhileWriting(data) {
  return request({
    url: '/api/alm/issueTag/findTagsByNameWhileWriting',
    method: 'post',
    data,
  })
}

// 标签新增/编辑
export function issueTagAddOrEdit(data, method) {
  return request({
    url: '/api/alm/issueTag',
    method: method,
    data,
  })
}
// 标签编辑
// export function issueTagEdit(data) {
//   return request({
//     url: '/api/alm/issueTag',
//     method: 'put',
//     data
//   })
// }

// 查询项目关联的主办或者辅办产品
export function getAllProductInfoList(projectId) {
  return request({
    url: `/api/alm/projectProduct/getAllProductInfoList/${projectId}`,
    method: 'GET',
  })
}

// 查询类型关联的工作流，流转过程中的必填字段
export function getRequiredFields(data) {
  return request({
    url: `/api/alm/alm/projectWorkflowTransitionCheck/query`,
    method: 'POST',
    data,
  })
}
// 项目下查询类型关联的工作流，流转过程中的必填字段
export function getRequiredFieldsOfProjectid(data) {
  return request({
    url: `/api/alm/alm/projectWorkflowTransitionCheck/findCheckList`,
    method: 'POST',
    data,
  })
}

// 保存类型关联的工作流，流转过程中的必填字段
export function saveRequiredFields(transitionId, data) {
  return request({
    url: `/api/alm/alm/projectWorkflowTransitionCheck/transition/${transitionId}`,
    method: 'post',
    data,
  })
}

// 查询工作流连线上的后处理字段
export function getTodoFields(data) {
  return request({
    url: `/api/alm/projectWorkflowTransitionAfterHandler/query`,
    method: 'POST',
    data,
  })
}

// 修改工作流连线上的后处理字段
export function putTodoFields(data) {
  return request({
    url: `/api/alm/projectWorkflowTransitionAfterHandler/batch`,
    method: 'POST',
    data,
  })
}

// 后处理字段删除
export function afterHandlerDel(data) {
  return request({
    url: '/api/alm/projectWorkflowTransitionAfterHandler',
    method: 'DELETE',
    data,
  })
}

// 需求拓扑图
export function showIssueTopology(requirementId) {
  return request({
    url: `/api/alm/alm/requirement/topology/${requirementId}`,
    method: 'GET',
  })
}

// 查询项目集权限
export function apiGetProjectProgramAuth(id) {
  return request({
    url: `/api/alm/projectProgramAuth/${id}`,
    method: 'GET',
  })
}

// 修改项目集权限
export function apiPutProjectProgramAuth(id, data) {
  return request({
    url: `/api/alm/projectProgramAuth/${id}`,
    method: 'PUT',
    data,
  })
}

// 需求中心需求列表
export function almReqmList(data) {
  return request({
    url: `/api/alm/alm/requirement/pageReqm`,
    method: 'POST',
    data,
  })
}

// 查询项目集下人员
export function apiGetProjectMUser(projectProgramId) {
  return request({
    url: `/api/alm/projectProgramAuth/getUserByProjectProgramId/${projectProgramId}`,
    method: 'GET',
  })
}

// 分页查询未完成的需求信息
export function unfinishedRequirement(data) {
  return request({
    url: `/api/insight/insight/projectComponent/unfinishedRequirement`,
    method: 'POST',
    data,
  })
}
// 分页查询未完成的风险信息
export function unfinishedRisk(data) {
  return request({
    url: `/api/insight/insight/projectComponent/unfinishedRisk`,
    method: 'POST',
    data,
  })
}
// 测试需求列表
export function testReqList(data) {
  return request({
    url: `/api/alm/alm/testreq/pageReqm`,
    method: 'POST',
    data,
  })
}
// 测试需求拓扑图
export function showTestIssueTopology(requirementId) {
  return request({
    url: `/api/alm/alm/testreq/topology/${requirementId}`,
    method: 'GET'
  })
}
