import request from '@/utils/axios-api'
// 根据项目id查询看板
export function getBoardForProject(id) {
  return request({
    url: `/api/alm/alm/projectKanban/findKanbanByProjectId/${id}`,
    method: `get`
  })
}

// 根据看板id 类型查询泳道
export function getBoardInfo(id, projectKanbanType, kanbanId) {
  return request({
    url: `/api/alm/alm/projectKanban/findKanbanByKanbanId/${id}/${projectKanbanType}/${kanbanId}`,
    method: `get`
  })
}
// 根据看板id 类型查询泳道
export function projectKanban(projectKanbanType, kanbanId) {
  return request({
    url: `/api/alm/alm/projectKanban/findKanbanByKanbanTypeAndKanbanId/${projectKanbanType}/${kanbanId}`,
    method: `get`
  })
}

// 查询看板数据
export function getBoardData(data) {
  return request({
    url: `/api/alm/alm/projectKanban/findKanbanItemByQuery`,
    method: `post`,
    data
  })
}
/**
 * @description 查询需求看板阶段和状态数据
 * @param {string} projectId 项目id
 * @param {string} projectKanbanType 看板类型
 * @param {string} kanbanId 看板id
 */
export function getBoardStages({ projectId, projectKanbanType, kanbanId }) {
  return request({
    url: `/api/alm/alm/projectKanban/findKanbanByKanbanStageId/${projectId}/${projectKanbanType}/${kanbanId}`,
    method: 'get'
  })
}
/**
 * @desc 查询项目下看板
 * @param {string} projectId 项目id
 */
export function getProjectBoards(projectId) {
  return request({
    url: `/api/alm/kanbanInfo/project/${projectId}`,
    method: 'get'
  })
}
/**
 * @desc 新增看板
 * @param {string} projectId 项目id
 * @param {*} data 参数
 */
export function addBoard(data) {
  return request({
    url: '/api/alm/kanbanInfo',
    method: 'post',
    data
  })
}
/**
 * @desc 修改看板
 * @param {string} projectId 项目id
 * @param {*} data 参数
 */
export function editBoard(data) {
  return request({
    url: '/api/alm/kanbanInfo',
    method: 'put',
    data
  })
}
/**
 * @desc 删除看板
 * @param {string} kanbanId 看板id
 */
export function deleteBoards(data) {
  return request({
    url: '/api/alm/kanbanInfo',
    method: 'delete',
    data
  })
}
/**
 * @desc 是否启用看板
 * @param {stirng} kanbanId 看板id
 * @param {boolean} enable 是否启用
 */
export function toggleBoardEnable(kanbanId, enable) {
  return request({
    url: `/api/alm/kanbanInfo/${kanbanId}/state/${enable}`,
    method: 'put'
  })
}
/**
 * @desc 查询项目看板信息
 * @param {string} id 看板id
 */
export function getBoardInfoByKanbanId(id) {
  return request({
    url: `/api/alm/kanbanInfo/${id}`,
    method: 'get'
  })
}
/**
 * @desc 查询看板列信息
 * @param {string} kanbanId 看板id
 * @param {string} name 列名称
 */
export function getBoardColumns(data) {
  return request({
    url: '/api/alm/kanbanColumn/query',
    method: 'post',
    data
  })
}
/**
 * @desc 查询看板状态配置信息
 * @param {string} kanbanId 看板id
 * @param {stirng} kanbanColumnId 看板列id
 * @param {string} classifyCode 事项分类id
 * @param {string} typeCode 类型id
 */
export function getBoardStatus(data) {
  return request({
    url: '/api/alm/kanbanColumnStatus/query',
    method: 'post',
    data
  })
}
/**
 * @desc 查询看板卡片配置信息
 * @param {string} kanbanId 看板id
 * @param {string} classifyCode 事项分类id
 * @param {string} typeCode 类型id
 */
export function getBoardCardConfig(data) {
  return request({
    url: '/api/alm/kanbanCard/query',
    method: 'post',
    data
  })
}
/**
 * @desc 查询看板事项信息
 * @param {string} planId 计划id
 */
export function getBoardCardsInfo(planId, data) {
  return request({
    url: `/api/alm/alm/issueItem/kanban/plan/${planId}`,
    method: 'post',
    data
  })
}
/**
 * @desc 查询工作项类型的状态工作流
 * @param {stirng} projectId 项目id
 */
export function getProjectWorkStatusFlow(projectId, data) {
  return request({
    url: `/api/alm/alm/projectWorkflow/${projectId}/findWorkflowNode`,
    method: 'post',
    data
  })
}
/**
 * @desc 修改项目看板排序
 * @param {*} data 排序的id
 */
export function sortProjectBoard(data) {
  return request({
    url: '/api/alm/kanbanInfo/sort',
    method: 'put',
    data
  })
}
/**
 * @desc 查询需求看板列数据
 * @param {}
 */
export function getIssueBoardColumns(typeCode) {
  return request({
    url: `/api/alm/kanbanInfo/requirement/${typeCode}`,
    method: 'get'
  })
}
/**
 * @desc 查询测试需求看板列数据
 * @param {}
 */
export function getTestIssueBoardColumns(typeCode) {
  return request({
    url: `/api/alm/kanbanInfo/testreq/${typeCode}`,
    method: 'get'
  })
}
/**
 * @desc 新增标志和备注
 * @param {}
 */
export function addFlagRemark(data) {
  return request({
    url: '/api/alm/alm/kanbanFlagRemark',
    method: 'post',
    data
  })
}
/**
 * @desc 查询标志和备注
 * @param {}
 */
export function getFlagRemark(data) {
  return request({
    url: '/api/alm/alm/kanbanFlagRemark/query',
    method: 'post',
    data
  })
}
/**
 * @desc 删除标志和备注
 * @param {}
 */
export function dalFlagRemark(data) {
  return request({
    url: '/api/alm/alm/kanbanFlagRemark',
    method: 'DELETE',
    data
  })
}
