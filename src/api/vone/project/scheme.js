import request from '@/utils/axios-api'

// 获取方案列表
export function getTestSchemeList(data) {
  return request({
    url: '/api/testm/testScheme/newPage',
    method: 'post',
    data
  })
}
// 删除
export function delTestScheme(data) {
  return request({
    url: '/api/testm/testScheme',
    method: 'delete',
    data
  })
}
// 新增
export function addTestScheme(data) {
  return request({
    url: '/api/testm/testScheme',
    method: 'post',
    data
  })
}
// 查询
export function getTestSchemeDetail(id) {
  return request({
    url: `/api/testm/testScheme/${id}`,
    method: 'get'
  })
}
// 编辑
export function editTestScheme(data) {
  return request({
    url: '/api/testm/testScheme',
    method: 'put',
    data
  })
}
// 获取spaceId
export function getSpaceId(projectId) {
  return request({
    url: `/api/wiki/wiki/space/getTestSchemeSpace/${projectId}`,
    method: 'get'
  })
}
// 获取方案可流转状态
export function getFlowStatus(schemeId) {
  return request({
    url: `/api/testm/testScheme/findNextNode/${schemeId}`,
    method: 'get'
  })
}
// 流转方案状态
export function flowSchemeStatus(schemeId, sourceStateCode, targetStateCode) {
  return request({
    url: `/api/testm/testScheme/transitionState/${schemeId}/${sourceStateCode}/${targetStateCode}`,
    method: 'put'
  })
}
