import request from '@/utils/axios-api'

/**
 * @desc 查询所有项目列表
 * @param {string} type
 */
export function getAllProject(data) {
  return request({
    url: `/api/alm/alm/project/page`,
    method: `post`,
    data,
  })
}

/**
 * @desc 查询所有项目列表
 * @param {string} type
 */
export function getAllProjectType(data) {
  return request({
    url: `/api/alm/alm/projectType/query`,
    method: `post`,
    data,
  })
}

/**
 * @desc 删除项目
 * @param {string} key 项目key
 */
export function deleteProject(params) {
  return request({
    url: `/api-alm/project/delProject`,
    method: 'get',
    params,
  })
}
/**
 * @desc 查询项目类型列表
 */
export function apiProjectGetProjectType(params) {
  return request({
    url: `/api-alm/project/getProjectType`,
    method: 'get',
    params,
  })
}
/**
 * @desc 查询所有人员
 */
export function apiBaseAllUser() {
  return request({
    url: '/api-base/api/base/rbac/user/search/all',
    method: 'get',
  })
}
/**
 * @desc 新增保存项目
 * @param {}
 */
export function apiProjectSave(data) {
  return request({
    url: `/api/alm/alm/project`,
    method: 'post',
    data,
  })
}
/**
 * @desc 查询高级属性模板
 * @param {}
 */
export function apiProjectGetProjectAdvance(params) {
  return request({
    url: `/api-alm/project/getProjectAttributeTemplate`,
    method: 'get',
    params,
  })
}
/**
 * @desc 查询组织机构
 * @param {}
 */
export function apiProjectGetOrgTree(params) {
  return request({
    url: `/api-base/api/base/rbac/org/tree`,
    method: 'get',
    params,
  })
}
/**
 * @desc 查询业务系统
 * @param {}
 */
export function apiProjectGetBusinessSystem(params) {
  return request({
    url: `/api-cmdb/api/cmdb/businessSystem`,
    method: 'get',
    params,
  })
}
/**
 * @desc 查询项目树
 * @param {}
 */
export function apiProjectGroupTree(params) {
  return request({
    url: `/api-alm/projectGroup/getProjectGroupTree`,
    method: 'get',
    params,
  })
}
/**
 * @desc 查询项目集人员
 * @param {string} projectGroupKey 项目key
 */
export async function apiGetGroupUser(projectGroupKey) {
  return request({
    url: `/api-alm/projectGroup/getGroupUser/${projectGroupKey}`,
    method: 'get',
  })
}
/**
 * @desc 查询项目下人员列表
 * @param {string} key 项目key
 */
export function getProjectUser(params) {
  return request({
    url: `/api-alm/project/project-user/list`,
    method: 'get',
    params,
  })
}
/**
 * 根据ID查询用户
 * @param {*} id
 */
export function apiProjectInfo(id) {
  return request({
    url: `/api/alm/alm/project/${id}`,
    method: 'get',
  })
}

/**
 * @desc 新增保存项目
 * @param {}
 */
export function apiProjectEdit(data) {
  return request({
    url: `/api/alm/alm/project`,
    method: 'put',
    data,
  })
}

// 项目删除
export function apiProjectDel(data) {
  return request({
    url: '/api/alm/alm/project',
    method: 'DELETE',
    data,
  })
}

/**
 * @desc 查询所有项目---不分页
 * @param {}
 */
export function apiAlmProjectNoPage(data) {
  return request({
    url: `/api/alm/alm/project/query`,
    method: 'post',
    data,
  })
}
/**
 * @desc 下拉条件查询项目列表无权限
 * @param {}
 */
export function queryListByCondition(data) {
  return request({
    url: `/api/alm/alm/project/queryListByCondition`,
    method: 'post',
    data,
  })
}
/**
 * @desc 查询所有产品--不分页
 * @param {string} type
 */
export function apiProductNoPage(data) {
  return request({
    url: `/api/product/product/productInfo/query`,
    method: `post`,
    data,
  })
}
/**
 * @desc 下拉框条件查询产品列表无权限
 * @param {string} type
 */
export function productListByCondition(data) {
  return request({
    url: `/api/product/product/productInfo/queryListByCondition`,
    method: `post`,
    data,
  })
}

/**
 * 根据项目id和事项类型查询工作流信息
 * @param {*}
 */
export function apiProjectmFindWorkflow(projectId, typeClassify, typeCode) {
  return request({
    url: `/api/alm/alm/projectWorkflow/findProjectByProjectIdAndTypeClassifyAndTypeCode/${projectId}/${typeClassify}/${typeCode}`,
    method: 'get',
  })
}

/**
 * @desc 根据条件批量查询项目人员
 * @param {string} projectId
 */
export function apiProjectUserNoPage(data) {
  return request({
    url: `/api/alm/projectTeam/user/query`,
    method: `post`,
    data,
  })
}

export function getUserList(data) {
  return request({
    url: `/api/base/base/user/getUsersByProjectId`,
    method: `post`,
    data,
  })
}

// 项目级下查询人员
export function apiProjectmUsers(data) {
  return request({
    url: `/api/alm/alm/projectUser/findByQuery`,
    method: `post`,
    data,
  })
}
/**
 * @desc 修改项目下工作流的权限
 * @param {string} projectId
 */
export function apiProjectWorkflowPut(data) {
  return request({
    url: `/api/alm/alm/projectWorkflow/updateAuthority`,
    method: `put`,
    data,
  })
}

/**
 * 根据项目id查询事项类型配置信息
 * @param {*}
 */
export function apiProjectFindAllType(projectId) {
  return request({
    url: `/api/alm/alm/projectTypeConfig/findAllByProjectId/${projectId}`,
    method: 'get',
  })
}

/**
 * @desc 修改事项启用状态
 * @param {string} projectId
 */
export function apiProjectChangeStatePut(data) {
  return request({
    url: `/api/alm/alm/projectTypeConfig`,
    method: `put`,
    data,
  })
}

//  项目集列表--不分页
export function apiProgramNoPage(data) {
  return request({
    url: '/api/alm/alm/projectProgram/query',
    method: 'post',
    data,
  })
}

// 项目概览---缺陷趋势
export async function apiProjectBugComponent(projectId) {
  return request({
    url: `/api/insight/insight/bugComponent/findTrendByProjectId/${projectId}`,
    method: 'get',
  })
}
// 查询项目无权限
export function findProjectInfoByCondition(data) {
  return request({
    url: '/api/alm/alm/project/findProjectInfoByCondition',
    method: 'post',
    data,
  })
}

// 批量编辑
export async function apiAlmUpdateAllByIds(typeClassify, data) {
  return request({
    url: `/api/alm/alm/issueBatchOperation/updateByIds/${typeClassify}`,
    method: 'POST',
    data,
  })
}

/**
 * @desc 修改项目下工作流
 * @param {string} projectId
 */
export function apiProjectWorkflowChartPut(data) {
  return request({
    url: `/api/alm/alm/projectWorkflow`,
    method: `put`,
    data,
  })
}

// 查看项目菜单权限
export async function apiProjectAuth(project_id) {
  return request({
    url: `/api/alm/alm/menu/${project_id}/router`,
    method: 'get',
  })
}

// 根据计划查项目列表
export async function getProjectInfoByPlanId(planId) {
  return request({
    url: `/api/testm/testPlan/getProjectInfoByPlanId/${planId}`,
    method: 'get',
  })
}
// 根据产品查项目列表
export async function getProjectProduct(productId, data) {
  return request({
    url: `/api/alm/projectProduct/getProject/${productId}`,
    method: 'get',
    data,
  })
}

// 修改项目流程状态
export async function editFlowStatus(data) {
  return request({
    url: `/api/alm/alm/project/updateProjectStatus`,
    method: 'POST',
    data,
  })
}

// 启动工作项发送站内信
export async function sendMsg(data) {
  return request({
    url: `/api/base/base/msg`,
    method: 'POST',
    data,
  })
}
// 启动工作项发送站内信
export function getIssueUpdateRecord(classify, itemId) {
  return request({
    url: `/api/alm/alm/issueUpdateRecord/${classify}/${itemId}`,
    method: 'get',
  })
}
// 移动工作项
export function getTargetTypeCode(typeClassify, itemId, targetTypeCode, data) {
  return request({
    url: `/api/alm/alm/issueItem/move/${typeClassify}/${itemId}/${targetTypeCode}`,
    method: 'post',
    data,
  })
}
// 工作项单项修改
export async function editById(classify, data) {
  return request({
    url: `/api/alm/alm/${classify}/updateById`,
    method: 'put',
    data,
  })
}
// 工作项状态查询用于筛选条件
export async function getWorkItemState(data) {
  return request({
    url: '/api/alm/alm/state/findByClassify',
    method: 'get',
    data,
  })
}

// 项目下查询工作流详情
export async function getFlowDetail(projectId, typeClassify, typeCode) {
  return request({
    url: `/api/alm/alm/projectWorkflow/findProjectByProjectIdAndTypeClassifyAndTypeCode/${projectId}/${typeClassify}/${typeCode}`,
    method: 'get',
  })
}

// 添加项目并启动项目流程
export async function addProjectAndStartFlow(data) {
  return request({
    url: `/api/camunda/processDefinition/start`,
    method: 'POST',
    data,
  })
}

// 项目结项
export async function closeProject(data) {
  return request({
    url: `/api/alm/alm/project/closure`,
    method: 'PUT',
    data,
  })
}

// 收藏项目
export async function collectProject(projectId, collection) {
  return request({
    url: `/api/alm/alm/project/collect?projectId=${projectId}&&collection=${collection}`,
    method: 'PUT',
  })
}
// 置顶项目
export async function topProject(projectId, top) {
  return request({
    url: `/api/alm/alm/project/top?projectId=${projectId}&&top=${top}`,
    method: 'PUT',
  })
}
// 一键添加项目经理
export async function batchUserToProject(opt, userIds) {
  return request({
    url: `/api/alm/alm/project/batchUserToProject/${opt}`,
    method: 'PUT',
    data: userIds,
  })
}
