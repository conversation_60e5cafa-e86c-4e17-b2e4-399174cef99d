import request from "@/utils/axios-api";

// 功能交易树形结构
export function getProductModule(id) {
  return request({
    url: `/api/product/product/productModuleFunction/findByProjectId/${id}`,
    method: "get",
  });
}
// 新增测试要点
export function addTestPoints(data) {
  return request({
    url: "/api/testm/testRequirementFunctionPoints",
    method: "post",
    data,
  });
}
// 修改测试要点
export function editTestPoints(data) {
  return request({
    url: "/api/testm/testRequirementFunctionPoints",
    method: "put",
    data,
  });
}
// 删除测试要点
export function deleteTestPoints(data) {
  return request({
    url: "/api/testm/testRequirementFunctionPoints",
    method: "delete",
    data,
  });
}
// 获取测试分析要点
export function getTestPoints(data) {
  return request({
    url: "/api/testm/testRequirementFunctionPoints/{id}",
    method: "get",
    data,
  });
}
// 获取测试分析要点分页
export function getTestPointsPage(data) {
  return request({
    url: "/api/testm/testRequirementFunctionPoints/page",
    method: "post",
    data,
  });
}
// 方案可流转状态
export function getFlowStatus(pointsId) {
  return request({
    url: `/api/testm/testRequirementFunctionPoints/findNextNode/${pointsId}`,
    method: 'get'
  })
}
// 流转方案状态
export function setFlowStatus(pointsId, sourceStateCode, targetStateCode) {
  return request({
    url: `/api/testm/testRequirementFunctionPoints/transitionState/${pointsId}/${sourceStateCode}/${targetStateCode}`,
    method: 'put'
  })
}
