<template>
  <div>
    <el-input v-model="filterText" style="margin-bottom: 10px;" placeholder="请输入名称" />
    <div class="treeBox">
      <el-tree ref="tree" :default-checked-keys="currentNodKey" node-key="id" :filter-node-method="filterNode" :expand-on-click-node="false" :data="treeData" :props="defaultProps" @node-click="nodeClick">
        <div slot-scope="{ node, data }" class="custom-tree-node">
          <el-tooltip class="item" effect="dark" :content="node.label" placement="top">
            <div class="treeNodeName">{{ node.label }}</div>
          </el-tooltip>
          <!-- <div class="treeNodeIcon" ()=> append(data)">
            <el-tooltip class="item" effect="dark" content="范围维护" placement="top">
              <i class="iconfont el-icon-application-setting icon_click"></i>
            </el-tooltip>
          </div> -->
        </div>
      </el-tree>
    </div>
    <RangeDialog v-if="rangeParamsData.visible" ref="RangeDialog" :space-visible.sync="rangeParamsData.visible" v-bind="rangeParamsData" @getTableData="getTree" />
  </div>
</template>

<script>
import _ from 'lodash'
import { getProductModule } from '@/api/vone/project/workitem'
import RangeDialog from './rangeDialog.vue'
export default {
  components: {
    RangeDialog
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      rangeParamsData: {
        visible: false,
        list: this.treeData
      },
      treeData: [],
      currentNodKey: []
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getTree()
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    append(data) {
      this.rangeParamsData = {
        visible: true
      }
    },
    async getTree() {
      const id = this.$route.params.id
      const res = await getProductModule(id)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.treeData = res.data
      this.currentNodKey = res.data[0]?.id ? [res.data[0]?.id] : []
    },
    nodeClick(data) {
      this.$emit('nodeClick', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.treeBox {
  margin-top: 10px;
  overflow-y: auto;
  height: calc(100vh - 165px - 42px);
}
::v-deep .el-tree-node {
  width: 100%;
  .el-tree-node__content {
    &:hover {
      .treeNodeIcon {
        display: block;
      }
    }
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  width: calc(100% - 40px);
  .treeNodeName {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .treeNodeIcon {
    display: none;
    position: sticky;
    right: 0;
    z-index: 100;
    .iconfont {
      color: var(--main-theme-color);
      cursor: pointer;
    }
  }
}
</style>
