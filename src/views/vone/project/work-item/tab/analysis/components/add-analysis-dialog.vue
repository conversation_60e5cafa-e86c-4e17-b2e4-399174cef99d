<template>
  <el-dialog :title="title" :visible="spaceVisible" :close-on-click-modal="false" :before-close="onClose">
    <el-form ref="formData" :model="formData" :rules="rules" label-position="top" label-width="100px" class="demo-ruleForm">
      <el-row>
        <el-col :span="12">
          <el-form-item label="功能点名称" prop="functionPoint">
            <el-input v-model="formData.functionPoint" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规则类型" prop="ruleType">
            <el-select v-model="formData.ruleType" placeholder="请选择规则类型">
              <el-option label="界面要素" value="界面要素" />
              <el-option label="业务规则" value="业务规则" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="formData.priority" placeholder="请选择优先级" filterable>
              <el-option v-for="item in prioritList" :key="item.id" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规则来源" prop="ruleSource">
            <el-input v-model="formData.ruleSource" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否涉账" prop="involveAccount">
            <el-select v-model="formData.involveAccount" placeholder="请选择是否涉账">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否涉及批量" prop="involveBatch">
            <el-select v-model="formData.involveBatch" placeholder="请选择是否涉及批量">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="测试要点" prop="testPoints">
        <el-input v-model="formData.testPoints" type="textarea" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onsave">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import _ from 'lodash'
import { addTestPoints, editTestPoints } from '@/api/vone/project/workitem'

export default {
  props: {
    spaceVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    },
    functionId: {
      type: String,
      default: ''
    },
    prioritList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      formData: {
        functionPoint: '',
        testPoints: '',
        ruleType: '',
        involveAccount: '',
        involveBatch: '',
        priority: '',
        ruleSource: ''
      },
      rules: {
        functionPoint: [
          { required: true, message: '请输入名称' }
        ],
        ruleType: [{ required: true, message: '请选择规则类型' }]
      },
      saveLoading: false,
      groupData: []
    }
  },
  mounted() {
    if (this.row) {
      this.formData = _.cloneDeep(this.row)
    }
  },
  methods: {
    async onsave() {
      try {
        await this.$refs.formData.validate()
      } catch (e) {
        return
      }
      this.loading = true
      const routeParams = this.$route.params
      const params = {
        issueTestReqId: routeParams.workId,
        functionId: this.functionId,
        ...this.formData
      }
      if (this.title == '新增测试点') {
        addTestPoints(params).then(async(res) => {
          this.loading = false
          if (res.isSuccess) {
            this.$message.success('保存成功')
            this.$emit('success')
            this.onClose()
          } else {
            this.$message.warning(res.msg)
          }
        })
      } else {
        editTestPoints(params).then(async(res) => {
          this.loading = false
          if (res.isSuccess) {
            this.$message.success('修改成功')
            this.$emit('success')
            this.onClose()
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
    },
    onClose() {
      this.$emit('update:spaceVisible', false)
      this.$refs['formData'].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
