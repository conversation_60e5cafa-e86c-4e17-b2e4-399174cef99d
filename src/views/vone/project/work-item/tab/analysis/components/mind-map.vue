<template>
  <div class="mind-map-box">
    <div id="mindMapContainer" />
    <!-- <div id="container">
      <div
        ref="miniMapContainer"
        :style="miniMapStyle"
        id="miniMapContainer"
      ></div>
      <div id="viewBoxContainer"></div>
    </div> -->
  </div>
</template>

<script>
import MindMap from 'simple-mind-map'
import MiniMap from 'simple-mind-map/src/plugins/MiniMap.js'
MindMap.usePlugin(MiniMap)
export default {
  data() {
    return {
      miniMapStyle: {},
      mindMap: null
    }
  },
  computed: {},
  mounted() {
    this.$nextTick(() => {
      this.mindMap = new MindMap({
        el: document.getElementById('mindMapContainer'),
        data: {
          data: {
            text: '测试要点分析'
          },
          children: [
            {
              data: {
                text: '模块'
              },
              children: [
                {
                  data: {
                    text: '测试节点'
                  },
                  children: []
                },
                {
                  data: {
                    text: '测试节点'
                  },
                  children: []
                }
              ]
            },
            {
              data: {
                text: '模块2'
              },
              children: []
            }
          ]
        }
      })
      // const miniMapData = mindMap.miniMap.calculationMiniMap("200px", "100px");
      // console.log(miniMapData, "-------");
      // this.miniMapStyle = {
      //   transform: `scale(${miniMapBoxScale})`,
      //   left: miniMapBoxLeft + "px",
      //   top: miniMapBoxTop + "px",
      // };
      // this.$refs.miniMapContainer.innerHTML = miniMapData.svgHTML;
    })
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
//@import "./simpleMindMap.esm.css";
#mindMapContainer {
  width: 100%;
  height: 100%;
}

#mindMapContainer * {
  margin: 0;
  padding: 0;
}
#container {
  width: 200px;
  height: 100px;
  position: relative;
  #miniMapContainer {
    position: absolute;
    transform-origin: left top;
    widows: 100px;
    height: 50px;
  }
  #viewBoxContainer {
    position: absolute;
  }
}
.mind-map-box {
  height: 100%;
}
</style>
