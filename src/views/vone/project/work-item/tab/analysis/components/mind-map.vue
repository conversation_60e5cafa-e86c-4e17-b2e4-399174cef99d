<template>
  <div class="mind-map-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-group">
        <el-select v-model="currentTheme" size="small" style="width: 110px;" @change="changeTheme">
          <el-option label="默认主题" value="default" />
          <el-option label="经典主题" value="classic" />
          <el-option label="小黄人" value="minions" />
          <el-option label="粉红葡萄" value="pinkGrape" />
          <el-option label="薄荷" value="mint" />
        </el-select>

        <el-select v-model="currentLayout" size="small" style="width: 110px;" @change="changeLayout">
          <el-option label="逻辑结构图" value="logicalStructure" />
          <el-option label="思维导图" value="mindMap" />
          <el-option label="目录组织图" value="catalogOrganization" />
          <el-option label="组织结构图" value="organizationStructure" />
          <el-option label="时间轴" value="timeline" />
        </el-select>
      </div>

      <div class="toolbar-group">
        <el-button size="small" icon="el-icon-download" @click="exportPng">
          导出PNG
        </el-button>
        <el-button size="small" icon="el-icon-document" @click="exportJson">
          导出JSON
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 思维导图画布 -->
      <div class="mind-map-wrapper">
        <div id="mindMapContainer" ref="mindMapContainer" />
      </div>

      <!-- 小地图 -->
      <div v-show="showMiniMap" class="minimap-container">
        <div class="minimap-header">
          <span>小地图</span>
          <el-button size="mini" type="text" @click="toggleMiniMap">
            <i class="el-icon-close" />
          </el-button>
        </div>
        <div ref="miniMapWrapper" class="minimap-content">
          <div
            ref="miniMapContainer"
            class="minimap-canvas"
            @mousedown="onMiniMapMouseDown"
            @mousemove="onMiniMapMouseMove"
          />
          <div
            ref="viewBoxContainer"
            class="minimap-viewbox"
            @mousedown="onViewBoxMouseDown"
          />
        </div>
      </div>

      <!-- 小地图切换按钮 -->
      <div v-show="!showMiniMap" class="minimap-toggle" @click="toggleMiniMap">
        <i class="el-icon-s-grid" />
      </div>

      <!-- 快捷操作栏 -->
      <div class="quick-toolbar">
        <div class="quick-toolbar-item" :class="{ disabled: !hasActiveNode }" @click="handleQuickAction('addNode')">
          <i class="iconfont el-icon-application-hang" />
          <span>添加子节点</span>
        </div>
        <div class="quick-toolbar-item" :class="{ disabled: !hasActiveNode }" @click="handleQuickAction('addSiblingNode')">
          <i class="iconfont el-icon-application-pipeline-scheduling" />
          <span>添加同级</span>
        </div>
        <div class="quick-toolbar-item" :class="{ disabled: !hasActiveNode }" @click="handleQuickAction('deleteNode')">
          <i class="el-icon-delete" />
          <span>删除节点</span>
        </div>
        <div class="quick-toolbar-divider" />
        <div class="quick-toolbar-item" :class="{ disabled: !canUndo }" @click="handleQuickAction('undo')">
          <i class="el-icon-refresh-left" />
          <span>撤销</span>
        </div>
        <div class="quick-toolbar-item" :class="{ disabled: !canRedo }" @click="handleQuickAction('redo')">
          <i class="el-icon-refresh-right" />
          <span>重做</span>
        </div>
        <div class="quick-toolbar-divider" />
        <div class="quick-toolbar-item" @click="handleQuickAction('zoomIn')">
          <i class="el-icon-zoom-in" />
          <span>放大</span>
        </div>
        <div class="quick-toolbar-item" @click="handleQuickAction('zoomOut')">
          <i class="el-icon-zoom-out" />
          <span>缩小</span>
        </div>
        <div class="quick-toolbar-item" @click="handleQuickAction('fit')">
          <i class="el-icon-full-screen" />
          <span>适应画布</span>
        </div>
        <div class="quick-toolbar-divider" />
        <div class="quick-toolbar-item" @click="handleQuickAction('exportPng')">
          <i class="el-icon-download" />
          <span>导出PNG</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MindMap from 'simple-mind-map'
import MiniMap from 'simple-mind-map/src/plugins/MiniMap.js'
import Export from 'simple-mind-map/src/plugins/Export.js'
import Drag from 'simple-mind-map/src/plugins/Drag.js'
import Select from 'simple-mind-map/src/plugins/Select.js'
import KeyboardNavigation from 'simple-mind-map/src/plugins/KeyboardNavigation.js'

// 注册插件
MindMap.usePlugin(MiniMap)
MindMap.usePlugin(Export)
MindMap.usePlugin(Drag)
MindMap.usePlugin(Select)
MindMap.usePlugin(KeyboardNavigation)

export default {
  name: 'MindMapDemo',
  data() {
    return {
      mindMap: null,
      showMiniMap: true,
      currentTheme: 'default',
      currentLayout: 'logicalStructure',
      hasActiveNode: false,
      canUndo: false,
      canRedo: false,
      isDragging: false,
      isViewBoxDragging: false,
      // 小地图相关数据
      miniMapData: null
    }
  },
  computed: {},
  mounted() {
    this.initMindMap()
    this.bindEvents()
  },
  beforeDestroy() {
    this.unbindEvents()
    if (this.mindMap) {
      this.mindMap.destroy()
    }
  },
  methods: {
    // 初始化思维导图
    initMindMap() {
      this.$nextTick(() => {
        this.mindMap = new MindMap({
          el: this.$refs.mindMapContainer,
          data: {
            data: {
              text: '测试要点分析'
            },
            children: [
              {
                data: {
                  text: '功能测试'
                },
                children: [
                  {
                    data: {
                      text: '用户界面测试'
                    },
                    children: []
                  },
                  {
                    data: {
                      text: '业务逻辑测试'
                    },
                    children: []
                  },
                  {
                    data: {
                      text: '数据验证测试'
                    },
                    children: []
                  }
                ]
              },
              {
                data: {
                  text: '性能测试'
                },
                children: [
                  {
                    data: {
                      text: '响应时间测试'
                    },
                    children: []
                  },
                  {
                    data: {
                      text: '并发测试'
                    },
                    children: []
                  }
                ]
              },
              {
                data: {
                  text: '兼容性测试'
                },
                children: [
                  {
                    data: {
                      text: '浏览器兼容性'
                    },
                    children: []
                  },
                  {
                    data: {
                      text: '设备兼容性'
                    },
                    children: []
                  }
                ]
              }
            ]
          },
          layout: this.currentLayout,
          theme: this.currentTheme,
          enableCtrlKeyNodeSelection: true,
          enableFreeDrag: false,
          readonly: false
        })

        // 初始化小地图
        this.initMiniMap()
      })
    },

    // 绑定事件
    bindEvents() {
      window.addEventListener('mouseup', this.onWindowMouseUp)
      window.addEventListener('mousemove', this.onWindowMouseMove)
    },

    // 解绑事件
    unbindEvents() {
      window.removeEventListener('mouseup', this.onWindowMouseUp)
      window.removeEventListener('mousemove', this.onWindowMouseMove)
    },

    // 初始化小地图
    initMiniMap() {
      if (!this.mindMap || !this.mindMap.miniMap) return

      // 监听思维导图数据变化
      this.mindMap.on('data_change', this.updateMiniMap)
      this.mindMap.on('view_data_change', this.updateMiniMap)
      this.mindMap.on('node_tree_render_end', this.updateMiniMap)

      // 监听节点激活状态变化
      this.mindMap.on('node_active', this.onNodeActiveChange)
      this.mindMap.on('node_unactive', this.onNodeActiveChange)

      // 监听历史记录变化
      this.mindMap.on('back_forward', this.updateHistoryState)
      this.mindMap.on('data_change', this.updateHistoryState)

      // 监听小地图视口框位置变化
      this.mindMap.on('mini_map_view_box_position_change', this.onViewBoxPositionChange)

      // 初始更新
      this.updateMiniMap()
      this.updateHistoryState()
    },

    // 更新小地图
    updateMiniMap() {
      if (!this.mindMap || !this.mindMap.miniMap || !this.$refs.miniMapWrapper) return

      const containerRect = this.$refs.miniMapWrapper.getBoundingClientRect()
      const miniMapData = this.mindMap.miniMap.calculationMiniMap(
        containerRect.width - 4, // 减去边框
        containerRect.height - 30 // 减去头部高度
      )

      if (miniMapData) {
        this.miniMapData = miniMapData

        // 更新小地图内容
        if (this.$refs.miniMapContainer) {
          this.$refs.miniMapContainer.innerHTML = miniMapData.svgHTML
          this.$refs.miniMapContainer.style.transform = `scale(${miniMapData.miniMapBoxScale})`
          this.$refs.miniMapContainer.style.left = miniMapData.miniMapBoxLeft + 'px'
          this.$refs.miniMapContainer.style.top = miniMapData.miniMapBoxTop + 'px'
        }

        // 更新视口框
        if (this.$refs.viewBoxContainer && miniMapData.viewBoxStyle) {
          Object.assign(this.$refs.viewBoxContainer.style, miniMapData.viewBoxStyle)
        }
      }
    },

    // 节点激活状态变化
    onNodeActiveChange() {
      this.hasActiveNode = this.mindMap.renderer.activeNodeList.length > 0
    },

    // 更新历史记录状态
    updateHistoryState() {
      if (this.mindMap && this.mindMap.command) {
        this.canUndo = this.mindMap.command.hasUndo()
        this.canRedo = this.mindMap.command.hasRedo()
      }
    },

    // 小地图相关事件处理
    onMiniMapMouseDown(e) {
      if (!this.mindMap || !this.mindMap.miniMap) return
      this.isDragging = true
      this.mindMap.miniMap.onMousedown(e)
    },

    onMiniMapMouseMove(e) {
      if (!this.isDragging || !this.mindMap || !this.mindMap.miniMap) return
      this.mindMap.miniMap.onMousemove(e, 5)
    },

    onViewBoxMouseDown(e) {
      if (!this.mindMap || !this.mindMap.miniMap) return
      e.stopPropagation()
      this.isViewBoxDragging = true
      this.mindMap.miniMap.onViewBoxMousedown(e)
    },

    onWindowMouseUp() {
      if (this.isDragging && this.mindMap && this.mindMap.miniMap) {
        this.mindMap.miniMap.onMouseup()
      }
      this.isDragging = false
      this.isViewBoxDragging = false
    },

    onWindowMouseMove(e) {
      if (this.isViewBoxDragging && this.mindMap && this.mindMap.miniMap) {
        this.mindMap.miniMap.onViewBoxMousemove(e)
      }
    },

    onViewBoxPositionChange({ left, right, top, bottom }) {
      if (this.$refs.viewBoxContainer) {
        const style = this.$refs.viewBoxContainer.style
        style.left = left + 'px'
        style.right = right + 'px'
        style.top = top + 'px'
        style.bottom = bottom + 'px'
      }
    },

    // 工具栏操作方法
    addNode() {
      if (this.mindMap) {
        this.mindMap.execCommand('INSERT_CHILD_NODE')
      }
    },

    addSiblingNode() {
      if (this.mindMap) {
        this.mindMap.execCommand('INSERT_NODE')
      }
    },

    deleteNode() {
      if (this.mindMap) {
        this.mindMap.execCommand('REMOVE_NODE')
      }
    },

    undo() {
      if (this.mindMap) {
        this.mindMap.execCommand('BACK')
      }
    },

    redo() {
      if (this.mindMap) {
        this.mindMap.execCommand('FORWARD')
      }
    },

    zoomIn() {
      if (this.mindMap) {
        this.mindMap.view.enlarge()
      }
    },

    zoomOut() {
      if (this.mindMap) {
        this.mindMap.view.narrow()
      }
    },

    resetZoom() {
      if (this.mindMap) {
        this.mindMap.view.reset()
      }
    },

    fit() {
      if (this.mindMap) {
        this.mindMap.view.fit()
      }
    },

    changeTheme(theme) {
      if (this.mindMap) {
        this.mindMap.setTheme(theme)
        this.currentTheme = theme
        // 强制更新小地图
        this.$nextTick(() => {
          this.updateMiniMap()
        })
      }
    },

    changeLayout(layout) {
      if (this.mindMap) {
        this.mindMap.setLayout(layout)
        this.currentLayout = layout
        // 强制更新小地图
        this.$nextTick(() => {
          this.updateMiniMap()
        })
      }
    },

    async exportPng() {
      if (this.mindMap && this.mindMap.doExport) {
        try {
          const data = await this.mindMap.doExport.png('思维导图', false)
          // 创建下载链接
          const a = document.createElement('a')
          a.href = data
          a.download = '思维导图.png'
          a.click()
          this.$message.success('PNG导出成功')
        } catch (error) {
          console.error('导出PNG失败:', error)
          this.$message.error('导出PNG失败，请稍后重试')
        }
      } else {
        console.error('导出功能不可用')
        this.$message.error('导出功能不可用')
      }
    },

    async exportJson() {
      if (this.mindMap && this.mindMap.doExport) {
        try {
          const data = await this.mindMap.doExport.json('', true)
          const a = document.createElement('a')
          a.href = data
          a.download = '思维导图数据.json'
          a.click()
          this.$message.success('JSON导出成功')
        } catch (error) {
          console.error('导出JSON失败:', error)
          // 备用方法
          const data = this.mindMap.getData()
          const dataStr = JSON.stringify(data, null, 2)
          const blob = new Blob([dataStr], { type: 'application/json' })
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = '思维导图数据.json'
          a.click()
          URL.revokeObjectURL(url)
          this.$message.success('JSON导出成功')
        }
      }
    },

    toggleMiniMap() {
      this.showMiniMap = !this.showMiniMap
      if (this.showMiniMap) {
        this.$nextTick(() => {
          this.updateMiniMap()
        })
      }
    },

    // 快捷操作栏事件处理
    handleQuickAction(action) {
      // 检查是否为禁用状态
      if (action === 'addNode' && !this.hasActiveNode) return
      if (action === 'addSiblingNode' && !this.hasActiveNode) return
      if (action === 'deleteNode' && !this.hasActiveNode) return
      if (action === 'undo' && !this.canUndo) return
      if (action === 'redo' && !this.canRedo) return

      // 执行对应的操作
      switch (action) {
        case 'addNode':
          this.addNode()
          break
        case 'addSiblingNode':
          this.addSiblingNode()
          break
        case 'deleteNode':
          this.deleteNode()
          break
        case 'undo':
          this.undo()
          break
        case 'redo':
          this.redo()
          break
        case 'zoomIn':
          this.zoomIn()
          break
        case 'zoomOut':
          this.zoomOut()
          break
        case 'fit':
          this.fit()
          break
        case 'exportPng':
          this.exportPng()
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mind-map-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.toolbar {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
  gap: 8px;
  min-height: 48px;

  .toolbar-group {
    display: flex;
    align-items: center;
    gap: 6px;

    &:not(:last-child) {
      border-right: 1px solid #e6e6e6;
      padding-right: 12px;
      margin-right: 4px;
    }

    .el-button {
      height: 32px;
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 4px;
    }

    .el-select {
      .el-input__inner {
        height: 32px;
        line-height: 32px;
        font-size: 12px;
      }
    }
  }
}

.main-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.mind-map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

#mindMapContainer {
  width: 100%;
  height: 100%;
  background: #fff;
}

#mindMapContainer * {
  margin: 0;
  padding: 0;
}

// 小地图样式
.minimap-container {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 250px;
  height: 180px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;

  .minimap-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #e6e6e6;
    font-size: 12px;
    color: #666;

    span {
      font-weight: 500;
    }
  }

  .minimap-content {
    position: relative;
    width: 100%;
    height: calc(100% - 30px);
    overflow: hidden;
  }

  .minimap-canvas {
    position: absolute;
    transform-origin: left top;
    cursor: pointer;
  }

  .minimap-viewbox {
    position: absolute;
    border: 2px solid #409eff;
    background: rgba(64, 158, 255, 0.1);
    cursor: move;
    transition: all 0.1s ease;
    pointer-events: auto;
  }
}

.minimap-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;

  &:hover {
    background: #f5f5f5;
    border-color: #409eff;

    i {
      color: #409eff;
    }
  }

  i {
    font-size: 18px;
    color: #666;
    transition: color 0.3s ease;
  }
}

// 快捷操作栏样式
.quick-toolbar {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 12px;
  z-index: 1000;
  gap: 4px;

  .quick-toolbar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 6px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 50px;
    user-select: none;

    &:hover:not(.disabled) {
      background: #f5f5f5;
      color: #409eff;

      i {
        color: #409eff;
      }
    }

    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    i {
      font-size: 16px;
      color: #666;
      margin-bottom: 2px;
      transition: color 0.2s ease;
    }

    span {
      font-size: 10px;
      color: #666;
      line-height: 1;
      text-align: center;
      transition: color 0.2s ease;
    }
  }

  .quick-toolbar-divider {
    width: 1px;
    height: 24px;
    background: #e6e6e6;
    margin: 0 4px;
  }
}
</style>
