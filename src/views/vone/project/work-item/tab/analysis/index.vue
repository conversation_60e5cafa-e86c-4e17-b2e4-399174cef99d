<template>
  <div class="pageBox contentbox">
    <div class="left pageContentNoH">
      <RangTree @nodeClick="nodeChangFn" />
    </div>
    <div class="card_content pageContentNoH">
      <vone-search-wrapper>
        <template slot="custom">
          <div class="date-tabs">
            <span
              :active="dateType === 'list'"
              @click="changeTab('list')"
            ><i
              class="iconfont el-icon-application-view-list"
            /></span>
            <span
              :active="dateType === 'mindMap'"
              @click="changeTab('mindMap')"
            ><i
              class="iconfont el-icon-application-branch"
            /></span>
          </div>
        </template>
        <template v-if="dateType === 'list'" slot="search">
          <vone-search-dynamic
            ref="searchForm"
            table-search-key="projectUserTable"
            :model.sync="formData"
            :default-fileds.sync="defaultFileds"
            show-basic
            :extra.sync="extraData"
            @getTableData="getTableData"
          />
        </template>
        <template slot="actions">
          <!-- <el-button v-if="dateType === 'list'" size="small" :disabled="!$permission('project_setting_role_add')" @click="clickAddAnalysis">
            AI辅助</el-button> -->
          <el-button
            v-if="dateType === 'list'"
            type="primary"
            icon="iconfont el-icon-tips-plus-circle"
            size="small"
            :disabled="!$permission('project_setting_role_add')"
            @click="clickAddAnalysis"
          >
            新增测试点</el-button>
          <el-button
            v-if="dateType === 'mindMap'"
            size="small"
            :disabled="!$permission('project_setting_role_add')"
            @click="clickAddAnalysis"
          >
            导入</el-button>
          <el-button
            v-if="dateType === 'mindMap'"
            size="small"
            :disabled="!$permission('project_setting_role_add')"
            @click="clickAddAnalysis"
          >
            导出</el-button>
          <el-button
            v-if="dateType === 'mindMap'"
            type="primary"
            size="small"
            :disabled="!$permission('project_setting_role_add')"
            @click="clickAddAnalysis"
          >
            保存</el-button>
          <el-dropdown
            v-if="dateType === 'list'"
            trigger="click"
            @command="(e) => e && e()"
          >
            <el-button
              class="btnMore"
            ><i
              class="iconfont el-icon-application-more"
            /></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item, index) in actions"
                :key="index"
                :icon="item.icon"
                :command="item.fn"
                :disabled="item.disabled"
              >
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template slot="fliter">
          <vone-search-filter
            :extra.sync="extraData"
            :model.sync="formData"
            :default-fileds.sync="defaultFileds"
            @getTableData="getTableData"
          />
        </template>
      </vone-search-wrapper>
      <main
        :style="{
          height:
            dateType === 'list' ? 'calc(100vh - 245px)' : 'calc(100vh - 210px)',
        }"
      >
        <vxe-table
          v-if="dateType === 'list'"
          ref="projectAnalysisTable"
          class="vone-vxe-table"
          border
          auto-resize
          height="auto"
          show-overflow="tooltip"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="tableData.records"
          :column-config="{ minWidth: '120px' }"
          :checkbox-config="{ reserve: true }"
          :row-config="{ keyField: 'id' }"
          @checkbox-all="selectAllEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-column type="checkbox" width="36" fixed="left" align="center" />
          <vxe-column title="功能点" field="functionPoint" min-width="180" />
          <vxe-column title="测试要点" field="testPoints" min-width="220" />
          <vxe-column title="规则类型" field="ruleType" min-width="120" />
          <vxe-column title="优先级" field="priority">
            <template #default="{ row }">
              <vone-icon-select
                v-model="row.priority"
                :data="prioritList"
                style="width: 100%"
                class="userList"
              >
                <el-option
                  v-for="item in prioritList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.code"
                >
                  <i
                    :class="`iconfont ${item.icon}`"
                    :style="{
                      color: item.color,
                      fontSize: '16px',
                      paddingRight: '6px',
                    }"
                  />
                  {{ item.name }}
                </el-option>
              </vone-icon-select>
            </template>
          </vxe-column>
          <vxe-column title="规则来源" field="ruleSource" />
          <vxe-column title="状态" field="stateCode" width="120">
            <template #default="{ row }">
              <el-dropdown
                trigger="click"
                placement="bottom"
                @command="(cd) => chapterFlowStatus(cd, row)"
              >
                <span class="el-dropdown-link">
                  <el-tag
                    effect="dark"
                    :type="row.stateCode === 'YSP' ? 'success' : ''"
                    @click="getFlowData(row)"
                  >
                    {{ stateTag(row) }}
                  </el-tag>
                </span>
                <el-dropdown-menu slot="dropdown" placement="bottom">
                  <el-dropdown-item
                    v-for="item in flowStatus"
                    :key="item.stateCode"
                    :command="item.stateCode"
                  >
                    {{ item.name }}
                  </el-dropdown-item>
                  <el-dropdown-item v-if="!flowStatus.length" command="NO">
                    暂无可流转状态
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </vxe-column>
          <vxe-column title="分析人" field="createdBy" min-width="120">
            <template #default="{ row }">
              <template v-if="row.createdBy && row.echoMap.createdBy">
                <vone-user-avatar
                  :avatar-path="row.echoMap.createdBy.avatarPath"
                  :name="row.echoMap.createdBy.name"
                />
              </template>
              <span v-else> -- </span>
            </template>
          </vxe-column>
          <vxe-column title="分析时间" field="createTime" min-width="120" />
          <vxe-column title="操作" fixed="right" align="left" width="120">
            <template #default="{ row }">
              <template>
                <!-- <el-tooltip class="item" content="菜单资源" placement="top">
                  <el-button type="text" icon="el-icon-tickets" @click="checkClickRow(row)" />
                </el-tooltip> -->
                <el-tooltip class="item" content="编辑" placement="top">
                  <el-button
                    type="text"
                    icon="iconfont el-icon-application-edit icon_click"
                    @click="editClickRow(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="删除" placement="top">
                  <el-button
                    type="text"
                    icon="iconfont el-icon-application-delete icon_click"
                    @click="deleteRow(row)"
                  />
                </el-tooltip>
              </template>
            </template>
          </vxe-column>
        </vxe-table>
        <MindMap v-if="dateType === 'mindMap'" />
      </main>
      <vone-pagination
        v-if="dateType === 'list'"
        ref="pagination"
        :total="tableData.total"
        @update="getTableData"
      />
    </div>
    <add-analysis-dialog
      v-if="paramsData.visible"
      ref="addAnalysisDialog"
      :space-visible.sync="paramsData.visible"
      v-bind="paramsData"
      @success="getTableData"
    />
    <RangeDialog
      v-if="rangeParamsData.visible"
      ref="RangeDialog"
      :space-visible.sync="rangeParamsData.visible"
      v-bind="rangeParamsData"
      @getTableData="getTableData"
    />
    <!-- 导入 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      :visible.sync="importParam.visible"
      type="analysis"
      @success="getTableData"
    />
  </div>
</template>

<script>
import list from './treeData.json'
import AddAnalysisDialog from './components/add-analysis-dialog.vue'
import RangeDialog from './components/rang-tree.vue'
import RangTree from './components/rang-tree.vue'
import {
  getTestPointsPage,
  deleteTestPoints,
  getFlowStatus,
  setFlowStatus
} from '@/api/vone/project/workitem'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import MindMap from './components/mind-map.vue'
import { download } from '@/utils'
import { apiBaseFileLoad } from '@/api/vone/base/file'

export default {
  components: {
    AddAnalysisDialog,
    RangeDialog,
    RangTree,
    MindMap
  },
  data() {
    return {
      searchName: '',
      list,
      tableData: {},
      tableLoading: false,
      formData: {
        projectId: this.$route.params.id,
        functionAndModuleId: '0',
        functionId: ''
      },
      defaultFileds: [
        {
          key: 'functionPoint',
          name: '功能点名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入名称'
        },
        {
          key: 'testPoints',
          name: '测试要点',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入测试要点'
        },
        {
          key: 'ruleType',
          name: '规则类型',
          type: {
            code: 'SELECT'
          },
          placeholder: '请输入测试要点'
        },
        {
          key: 'ruleType',
          name: '规则类型',
          type: {
            code: 'SELECT'
          },
          placeholder: '请输入测试要点',
          optionList: [
            {
              code: '界面要素',
              name: '界面要素',
              id: '1'
            },
            {
              code: '业务规则',
              name: '业务规则',
              id: '2'
            }
          ]
        }
      ],
      extraData: {},
      actions: [
        {
          name: '批量删除',
          icon: 'iconfont el-icon-application-delete',
          fn: this.deleteAll
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie
        }
      ],
      dateType: 'list',
      paramsData: {
        visible: false,
        title: '新增测试点'
      },
      rangeParamsData: {
        visible: false
      },
      prioritList: [],
      nodeId: '',
      selecteTableData: [],
      importParam: { visible: false },
      flowStatus: []
    }
  },
  computed: {
    stateTag() {
      return function(row) {
        return row.echoMap?.stateCode?.name || ''
      }
    }
  },
  mounted() {
    this.getPrioritList()
  },
  methods: {
    async chapterFlowStatus(cd, row) {
      if (cd && cd !== 'NO') {
        const { isSuccess, msg } = await setFlowStatus(
          row.id,
          row.stateCode,
          cd
        )
        if (!isSuccess) return this.$message(msg)
        this.getTableData()
      }
    },
    async getFlowData(row) {
      try {
        const { data, isSuccess, msg } = await getFlowStatus(row.id)
        if (!isSuccess) return this.$message.warning(msg)
        this.flowStatus = data || []
      } catch {
        return
      }
    },
    // 导入
    imPort() {
      console.log('123')
      this.importParam = {
        visible: true,
        title: '项目-测试分析',
        url: `/api/testm/testRequirementFunctionPoints/excel/template`,
        importUrl: `/api/testm/testRequirementFunctionPoints/excel/import`,
        data: { projectId: this.$route.params.id }
      }
    },
    selectAllEvent({ checked }) {
      this.selecteTableData =
        this.$refs.projectAnalysisTable.getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.selecteTableData =
        this.$refs.projectAnalysisTable.getCheckboxRecords()
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    nodeChangFn(node, data) {
      console.log(node, data)
      this.nodeId = node.id
      this.$set(this.formData, 'functionId', node.id)
      this.getTableData()
    },
    // 查询列表
    async getTableData() {
      this.tableLoading = true
      const pageObj = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20
      }
      this.$set(pageObj, 'sort', 'createTime')
      this.$set(this.formData, 'projectId', this.$route.params.id)
      this.$set(this.formData, 'functionAndModuleId', '0')

      const params = {
        ...pageObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await getTestPointsPage(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    clickAddAnalysis() {
      this.paramsData = {
        visible: true,
        title: '新增测试点',
        functionId: this.nodeId,
        prioritList: this.prioritList
      }
    },
    editClickRow(row) {
      this.paramsData = {
        visible: true,
        row,
        title: '编辑测试点',
        functionId: this.nodeId,
        prioritList: this.prioritList
      }
    },
    // 导出
    async exportFlie() {
      try {
        this.tableLoading = true
        download(
          `测试分析.xls`,
          await apiBaseFileLoad(
            '/api/testm/testRequirementFunctionPoints/excel/export',
            this.formData
          )
        )
        this.tableLoading = false
      } catch (e) {
        this.tableLoading = false
        return
      }
    },
    changeTab(e) {
      this.dateType = e
    },
    append(e) {
      console.log(e)
      this.rangeParamsData = {
        visible: true
      }
    },
    async deleteRow(data) {
      await this.$confirm(`确定删除【${data.functionPoint}】吗`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })
      const res = await deleteTestPoints([data.id])

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.$message.success('删除成功')
      this.getTableData()
    },
    async deleteAll() {
      if (!this.selecteTableData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除当前数据?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        })
        this.tableLoading = true
        const selectId = this.selecteTableData.map((r) => r.id)
        const res = await deleteTestPoints(selectId)
        this.tableLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.tableLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.contentbox {
  display: flex;
}
.left {
  height: calc(100vh - 48px - 48px - 20px - 10px);
  padding: 10px;
  overflow-y: auto;
  width: 240px;
  margin-right: 10px;
  .treeBox {
    margin-top: 10px;
    overflow-y: auto;
    height: calc(100vh - 165px - 42px);
  }
}
.card_content {
  height: calc(100vh - 48px - 48px - 20px - 10px);
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  position: relative;
  .date-tabs {
    margin-right: 12px;
    background: #f2f3f5;
    border-radius: 2px;
    padding: 4px;
    height: 32px;
    > span {
      color: var(--auxiliary-font-color);
      font-size: 14px;
      cursor: pointer;
      height: 24px;
      width: 32px;
      text-align: center;
      line-height: 24px;
      display: inline-block;
      border-radius: 2px;
      &[active] {
        background: #fff;
        color: #3e7bfa;
        font-weight: 500;
      }
    }
  }
}
::v-deep .el-tree-node {
  width: 100%;
  .el-tree-node__content {
    &:hover {
      .treeNodeIcon {
        display: block;
      }
    }
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  width: calc(100% - 40px);
  .treeNodeName {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .treeNodeIcon {
    display: none;
    position: sticky;
    right: 0;
    z-index: 100;
    .iconfont {
      color: var(--main-theme-color);
      cursor: pointer;
    }
  }
}
.userList {
  pointer-events: none;
  ::v-deep .el-input__inner {
    border: 0;
  }
  ::v-deep .el-input__icon {
    display: none;
  }
}
</style>
