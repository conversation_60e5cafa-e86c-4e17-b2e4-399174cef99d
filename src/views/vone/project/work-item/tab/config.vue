<template>
  <overview v-if="type == 'overview'" :work-id="workId" />
  <testCase v-else-if="type == 'case'" />
  <defect v-else-if="type == 'defect'" />
  <analysis v-else-if="type === 'analysis'" />
  <execution v-else-if="type === 'execution'" />
  <report v-else-if="type === 'report'" />
  <task v-else-if="type === 'task'" />
  <testPlan v-else-if="type === 'testPlan'" />
  <config v-else :type="type" />
</template>

<script>
// import overview from './overview'
import overview from '@/views/vone/project/components/work-item/custom-info'

import testCase from './case'
import defect from './defect'
import analysis from './analysis/index'
import execution from './execution'
import report from './report'
import task from './task'
import testPlan from './testPlan'

export default {
  components: {
    overview,
    testCase,
    defect,
    analysis,
    execution,
    report,
    task,
    testPlan
  },
  props: {
    type: {
      default: undefined,
      type: String
    },
    name: {
      default: undefined,
      type: String
    },
    workId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style></style>
